﻿@model GlobalTrader2.Dto.Templates.ReleasedHUBRFQMailTemplate
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HUB RFQ Notification</title>
    <style type="text/css">
        body {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            background-color: #ffffff;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }
        
        .data-table {
            border: 1px solid #000000;
        }

        th, td {
            padding: 8px; 
            text-align: left;
        }

        .header-class {
            font-size: 13px;
            font-weight: bold;
            color: #FFFFFF;
        }

        .header-class-2 {
            font-size: 13px;
            font-weight: bold;
        }

        .text-class {
            font-size: 11px;
        }

        .style-one {
            font-size: 11px;
            font-weight: bold;
        }

        .style-two {
            font-weight: bold;
            font-size: 24px;
        }

        .table-header-row {
            background-color: #F5F5F5;
        }

        .text-center {
            text-align: center;
        }

        .width-50-percent {
            width: 50%;
        }
    </style>
</head>
<body>
    <table class="data-table" role="presentation">
        <tr>
            <td colspan="2" class="text-center">
                <h3>#HUBRFQSTATUS#</h3>
            </td>
        </tr>
        <tr>
            <td class="text-class width-50-percent">
                <strong>Code </strong> : #Code#
            </td>
            <td class="text-class">
                <strong>Contact</strong> : #Contact#
            </td>
        </tr>
        <tr>
            <td class="text-class">
                <strong>Name</strong> : #Name# &nbsp;
            </td>
            <td class="text-class">
                <strong>Currency</strong> : #Currency#
            </td>
        </tr>
        <tr>
            <td class="text-class">
                <strong>Company</strong> : #Company# &nbsp;
            </td>
            <td class="text-class">
                <strong>Quote Required </strong> : #QuoteRequired#
            </td>
        </tr>
    </table>
    <br>
    <div><strong>Customer Requirement</strong></div>
    <table class="data-table">
        <thead>
            <tr class="table-header-row">
                <th scope="col" class="text-class">Req <br> Type</th>
                <th scope="col" class="text-class"> Quantity <br> Traceability</th>
                <th scope="col" class="text-class">Part No <br> Customer Part</th>
                <th scope="col" class="text-class"> Mfr <br> DC </th>
                <th scope="col" class="text-class">Customer <br> Required </th>
                <th scope="col" class="text-class"> Target Price <br> Salesperson </th>
            </tr>
        </thead>
        <tbody>
            #trline#
        </tbody>
    </table>
    <br>
    <div><strong>Quotes to Client</strong></div>
    <table class="data-table">
        <thead>
            <tr class="table-header-row">
                <th scope="col" class="text-class">Supplier Type <br> Related Quotes</th>
                <th scope="col" class="text-class"> PartNo <br> Notes</th>
                <th scope="col" class="text-class">MFR <br> Date Code</th>
                <th scope="col" class="text-class"> Product <br> Package </th>
                <th scope="col" class="text-class">Date Offered <br> By </th>
                <th scope="col" class="text-class"> Quantity <br> Delivery Date </th>
                <th scope="col" class="text-class"> Unit Sell Price <br> Base Currency </th>
                <th scope="col" class="text-class"> Price Request <br> Region </th>
                <th scope="col" class="text-class"> Estimated Shipping Cost <br> Base Currency </th>
            </tr>
        </thead>
        <tbody>
            #trSrline#
        </tbody>
    </table>
</body>
</html>