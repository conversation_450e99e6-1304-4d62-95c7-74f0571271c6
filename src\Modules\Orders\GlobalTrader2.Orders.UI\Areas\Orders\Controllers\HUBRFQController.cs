using GlobalTrader2.Aggregator.UseCases.DataList.GetFilterBOMLinesList;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMItemDetailFlow.Queries;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMNoBidRequirementFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMReleaseAllFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.CustomerRequirementNoBidFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.PartWatch.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.NotifyPurchaseRequestBom.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.RLStockNotification.Commands;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Orders.UI.ViewModel;
using GlobalTrader2.Orders.UI.ViewModel.BOM;
using GlobalTrader2.Orders.UI.ViewModel.HubFRQ;
using GlobalTrader2.Orders.UI.ViewModel.HubRFQ;
using GlobalTrader2.Orders.UserCases.Orders.BOM;
using GlobalTrader2.Orders.UserCases.Orders.BOM.AssignmentHistory.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMSourcingResultForRelease.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMStatusToClosed.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CustomerRequirementRecallNoBid.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ExpediteNote.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ExportToExcel.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQCommunicationNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQHasRLStock.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query;
using GlobalTrader2.Orders.UserCases.Orders.BOM.NotifyMessageSupplier.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.PurchaseQuote.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.GetLyticaManufacturer;
using GlobalTrader2.Orders.UserCases.Orders.BOM.UpdateCustRequirementByBomID.Commands;
using GlobalTrader2.Orders.UserCases.Orders.PVVBOM.Commands;
using GlobalTrader2.Orders.UserCases.Orders.PVVBOM.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.PVVBOMQuestion.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DeleteBomItem.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.UnReleaseBomItem.Commands;
using GlobalTrader2.Orders.UserCases.Orders.TempPVVBOMQuestion.Commands;
using GlobalTrader2.Orders.UserCases.Orders.TempPVVBOMQuestion.Queries.Dtos;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Globalization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/bom")]
public class HubrfqController(IMediator mediator, SessionManager sessionManager,
    IMapper mapper,
    IStringLocalizer<SharedUI.ReportTitles> reportTitlesLocalizer,
    IStringLocalizer<SharedUI.Reports> reportLocalizer,
    IStringLocalizer<MessageResources> messageLocalizer,
    IStringLocalizer<SharedUI.Misc> miscLocalizer,
    IConfiguration configuration,
    IExportService exportService) : ApiBaseController
{
    private readonly IMediator _mediator = mediator;
    private readonly IMapper _mapper = mapper;
    private readonly SessionManager _sessionManager = sessionManager;
    private readonly IStringLocalizer<SharedUI.Misc> _miscLocalizer = miscLocalizer;
    private readonly IStringLocalizer<SharedUI.Reports> _reportLocalizer = reportLocalizer;
    private readonly IStringLocalizer<MessageResources> _messageLocalizer = messageLocalizer;
    private readonly IExportService _exportService = exportService;
    private readonly IConfiguration _configuration = configuration;

    [HttpGet("search-id")]
    public async Task<IActionResult> GetHUBRFQIdByNumber([FromQuery] string bomName)
    {
        return Ok(await _mediator.Send(new GetBomIdByNameQuery()
        {
            ClientNo = ClientId,
            BomName = bomName,
        }));
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetCustomerRequirement(int id)
    {
        var result = await _mediator.Send(new GetBOMDetailsQuery
        {
            Id = id,
            LoginId = UserId
        });
        if (result.Data is not null)
        {
            result.Data.LastUpdated = LocalizerHelper.FormatDLUP(result.Data.DLUP, result.Data.UpdatedByEmployeeName, _miscLocalizer, CultureInfo.CurrentCulture);
        }
        return Ok(result);
    }

    [HttpGet("communication-note/{id}")]
    public async Task<IActionResult> GetCommunicationNotes(int id)
    {
        return Ok(await _mediator.Send(new GetAllHubrfqCommunicationNoteQuery
        {
            HUBRFQId = id,
            ClientID = ClientId
        }));
    }

    [HttpPost("send-to-purchase-hub")]
    public async Task<IActionResult> SendToPurchaseHub(SendToPurchaseHubRequest request, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new UpdatePurchaseQuoteCommand()
        {
            BOMId = request.Id,
            UpdatedBy = UserId,
            BOMStatus = (int)BOMStatus.RPQ,
            AssignUserNo = request.AssignUserNo
        }, cancellationToken);
        if (result.Success)
        {
            List<int> tologinIds = [request.AssignUserNo];
            var mailGroupId = _sessionManager.GetInt32(SessionKey.POHubMailGroupId) ?? 0;

            // Send notification to the purchase hub
            await _mediator.Send(new CreateNotifyPurchaseRequestBomCommand
            {
                ToLogins = [.. tologinIds],
                ToGroups = [mailGroupId],
                Subject = string.Format(_messageLocalizer["PurchaseRequest"], request.Name),
                BomCode = request.Code,
                BomId = request.Id,
                BomName = request.Name,
                BomCompanyName = request.Company,
                BomCompanyNo = request.CompanyNo,
                LoginEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty,
                LoginId = UserId,
                RecipientLoginIDsCC = request.RecipientLoginIDs ?? [],
                IsPOHub = _sessionManager.IsPOHub,
                ClientName = _sessionManager.GetString(SessionKey.ClientName) ?? string.Empty,
                UrlBase = RequestHelper.GetApplicationUrl(HttpContext.Request),
                Sender = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
            }, cancellationToken);

            // Get customer requirements
            var customerRequirementsRes = await _mediator.Send(new GetAllHUBRFQHasRLStockQuery
            {
                BomId = request.Id
            }, cancellationToken);

            // Send RLStock
            await _mediator.Send(new CreateRLStockNotificationCommand
            {
                BOMCode = request.Code,
                BomCompanyName = request.Company,
                CustomerRequirements = _mapper.Map<IList<HUBRFQHasRLStockDto>>(customerRequirementsRes.Data),
                Id = request.Id,
                IsFromBOM = true,
                LoginID = UserId,
                Subject = string.Format(_messageLocalizer["RLStockSubject1"], request.Code),
                HubRFQLink = $"{RequestHelper.GetApplicationUrl(HttpContext.Request)}/Orders/HUBRFQ/Details?bom={request.Id}",
                Sender = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
                SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty
            }, cancellationToken);

            return Ok(result);
        }
        return Ok();
    }

    [HttpPost("release-all")]
    public async Task<IActionResult> ReleaseAll(BOMReleaseAllRequest request, CancellationToken cancellationToken)
    {
        var reqSalePerson = 0;
        var supportTeamMemberNo = 0;

        if (_sessionManager.IsPOHub)
        {
            int.TryParse(request.ReqSalesPerson.TrimEnd('|'), out reqSalePerson);
            int.TryParse(request.SupportTeamMemberNo.TrimEnd('|'), out supportTeamMemberNo);
        }

        var urlPath = Navigations.BOMDetailWithId(request.Id).CtaUri;
        var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);

        return Ok(await _mediator.Send(new BomReleaseAllFlowCommand
        {
            BomId = request.Id,
            UpdatedBy = UserId,
            LoginEmail = LoginEmail,
            Subject = $"{_messageLocalizer["HUBRFQReleased"]} ( {request.Name} )",
            BOMCode = request.Code,
            BomCompanyName = request.Company,
            BomCompanyNo = request.CompanyNo,
            RequestedBy = request.RequestToPOHubBy ?? 0,
            ReqSalesPerson = reqSalePerson,
            SupportTeamMemberNo = supportTeamMemberNo,
            BOMName = request.Name,
            ClientId = ClientId,
            LoginId = UserId,
            SenderName = string.Format(_sessionManager.LoginFullName),
            HUBRFQStatus = _messageLocalizer["HUBRFQReleased"],
            ClientName = _sessionManager.ClientName!,
            CustomerRequirementId = request.CustomerRequirementId,
            IsPoHub = _sessionManager.IsPOHub,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            HUBRFQHyperLink = $"{urlBase}{urlPath}"
        }, cancellationToken));
    }


    [HttpPost("list")]
    public async Task<IActionResult> GetBOMLines(GetBOMLineRequest request)
    {
        var isRequirementSearch = Enum.TryParse<BOMQueryMode>(request.HeaderOrDetail, true, out var mode)
                         && mode == BOMQueryMode.Detail;
        var isPOHub = _sessionManager.IsPOHub;

        var queryResult = await _mediator.Send(new GetBOMLinesQuery()
        {
            Status = request.Status ?? 0,// BomStatus
            AssignedUser = request.AssignedUser, //PoHubBuyer
            Manufacturer = request.Manufacturer?.Trim(), //Manufacturer
            Part = request.Part != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.Part) : null,
            IsSearchFromRequirements = isRequirementSearch,
            StartDate = request.StartDate,
            EndDate = request.EndDate,
            Division = request.Division, //Division
            IsPoHub = isPOHub,
            SalesPersonId = request.SalesPersonId, // SalesPerson
            CompanyTypeId = request.CompanyTypeId, // CompanyType
            AS6081Required = request.AS6081Required ?? 0,
            SelectedClientId = request.SelectedClientId,
            RequiredEndDate = request.RequiredEndDate,
            RequiredStartDate = request.RequiredStartDate,
            Name = request.Name?.Trim(),
            Code = request.Code?.Trim(),
            LoginId = _sessionManager.LoginID!.Value,
            DivisionId = _sessionManager.LoginDivisionID,
            ClientCurrencyCode = _sessionManager.ClientCurrencyCode!,
            TeamId = _sessionManager.LoginTeamID,
            ClientId = ClientId,
            ViewLevelList = request.ViewLevelList,
            OrderBy = request.OrderBy ?? 0,
            SortDir = request.SortDir ?? 1,
            Index = request.Index,
            Size = request.Size,
            IsGSA = _sessionManager.IsGSA,
            IsGlobalUser = _sessionManager.IsGlobalUser
        });

        var resultView = _mapper.Map<BaseResponse<IEnumerable<BOMLineViewModel>>>(queryResult);
        var totalItems = queryResult.Data!.FirstOrDefault()?.RowCnt ?? 0;

        var blnMakeYellow = (_sessionManager.IsGSA && !_sessionManager.IsGlobalUser && request.SelectedClientId.HasValue);
        foreach (var bomLine in resultView.Data!.ToList())
        {
            bomLine.BlnMakeYellow = blnMakeYellow;
            bomLine.IsPOHub = isPOHub;
        }

        var response = new DatatableResponse<IEnumerable<BOMLineViewModel>>()
        {
            Success = resultView.Success,
            Data = resultView.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPost("bom-bid-requirement")]
    public async Task<IActionResult> CreateBomBidRequirement(BOMNoBidRequirementRequest request)
    {
        return Ok(await _mediator.Send(new CreateBOMNoBidRequirementFlowCommand
        {
            BomId = request.BomId,
            Notes = request.BidNotes,
            UpdatedBy = UserId,
            LoginEmail = LoginEmail,
            Subject = $"{_messageLocalizer["HUBRFQNoBid"]} ( {request.BomName} )",
            BOMCode = request.BomCode,
            BomCompanyName = request.BomCompanyName,
            BomCompanyNo = request.BomCompanyNo,
            BOMName = request.BomName,
            ClientId = ClientId,
            LoginId = UserId,
            SalesmanNo = request.SalesmanNo,
            SenderName = string.Format(_sessionManager.LoginFullName),
            HUBRFQStatus = _messageLocalizer["HUBRFQNoBid"],
            ClientName = _sessionManager.ClientName!,
        }));
    }

    [HttpPost("add-new")]
    public async Task<IActionResult> AddNew(AddNewBomRequest request)
    {
        return Ok(await _mediator.Send(new CreateNewBomCommand
        {
            AS9120 = request.AS9120,
            AssignUserNo = request.AssignUserNo ?? 0,
            ClientID = ClientId,
            Company = request.Company,
            CompanyName = request.CompanyName?.Trim(),
            Contact = request.Contact,
            Contact2 = request.Contact2,
            CurrencyNo = request.CurrencyNo,
            CurrentSupplier = request.CurrentSupplier?.Trim(),
            GeneratedBomID = request.GeneratedBomID?.Trim(),
            GeneratedFilename = request.GeneratedFilename?.Trim(),
            Inactive = request.Inactive,
            LoginEmail = LoginEmail.Trim(),
            LoginID = UserId,
            Name = request.Name?.Trim(),
            Notes = request.Notes?.Trim(),
            OriginalFilename = request.OriginalFilename?.Trim(),
            QuoteRequired = request.QuoteRequired,
            RecipientLoginIDs = request.RecipientLoginIDs,
            Subject = "Subject"
        }));
    }

    [HttpGet("part-detail/{customerRequirementId}/sourcing-results")]
    public async Task<IActionResult> GetSourcingResults(int customerRequirementId)
    {
        var query = new GetListForBOMCustomerRequirementQuery
        {
            ClientId = ClientId,
            CustomerRequirementId = customerRequirementId,
            IsPOHub = _sessionManager.IsPOHub,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            LoginId = UserId
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpGet("{bomId}/pvv-items")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
    public async Task<IActionResult> GetPvvBomItems(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetAllPvvBomQuery { BomId = bomId };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("{bomId}/sourcing-for-release")]
    public async Task<IActionResult> GetSourcingForRelease(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetBOMSourcingResultForReleaseQuery { BomId = bomId, IsPoHub = _sessionManager.IsPOHub };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("{bomId}/pvv-questions")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
    public async Task<IActionResult> GetPvvBomQuestionItems(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetAllPvvBomQuestionQuery
        {
            BomId = bomId,
            IsPoHub = sessionManager.IsPOHub
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("get-temp-pvv-questions")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)] 
    public async Task<IActionResult> GetTempPvvBomItems([FromQuery] string? bomIdGenerated, CancellationToken cancellationToken)
    {
        var query = new GetAllTempPvvBomQuestionQuery
        {
            BomIdGenerated = bomIdGenerated,
            IsPoHub = sessionManager.IsPOHub
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpDelete("delete-pvv-questions/{id}")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> DeletePvvBomItems(int id, CancellationToken cancellationToken)
    {
        var query = new DeletePvvBomCommand
        {
            PVVBOMId = id,
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpPost("{bomId}/pvv-answers")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> UpdatePvvAnswers(int bomId, [FromBody] SaveEditTempPvvAnswersRequest request, CancellationToken cancellationToken)
    {
        var command = new UpdatePvvBomCommand
        {
            BomNo = bomId,
            ClientId = sessionManager.ClientID ?? 0,
            UpdatedBy = sessionManager.LoginID,
            Notes = request.PVVAnswers
        };

        return Ok(await _mediator.Send(command, cancellationToken));
    }

    [HttpGet("{bomId}/get-check-data")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
    public async Task<IActionResult> GetPvvBomQuestionCheckData(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetAllCheckPvvBomQuery
        {
            BomId = bomId,
            IsPoHub = sessionManager.IsPOHub
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpPost("temp-pvv-answers")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> SaveEditTTempPvvAnswers([FromBody] SaveEditTempPvvAnswersRequest request, CancellationToken cancellationToken)
    {
        var command = new UpdateTempPvvAnswerCommand
        {
            BomGeneratedId = request.BomIdGenerated,
            ClientNo = sessionManager.ClientID ?? 0,
            UpdatedBy = sessionManager.LoginID,
            Notes = request.PVVAnswers
        };

        var result = await _mediator.Send(command, cancellationToken);

        return Ok(result);
    }

    [HttpPost("send-mail-message-supplier")]
    public async Task<IActionResult> SendMailMessageSupplier(SendMailMessageSupplierRequest request)
    {
        var resources = new List<(string key, string value)>();

        var reportTitles = reportTitlesLocalizer.GetAllStrings();
        foreach (var reportTitle in reportTitles)
        {
            resources.Add(($"ReportTitles{reportTitle.Name}", reportTitle.Value));
        }

        var reportLocalizerLocal = _reportLocalizer.GetAllStrings();
        foreach (var report in reportLocalizerLocal)
        {
            resources.Add(($"Reports{report.Name}", report.Value));
        }

        var miscLocalizerLocal = _miscLocalizer.GetAllStrings();
        foreach (var misc in miscLocalizerLocal)
        {
            resources.Add(($"Misc{misc.Name}", misc.Value));
        }
        return Ok(await _mediator.Send(new CreateNotifyMessageSupplierCommand
        {
            CurrencyCode = request.CurrencyCode,
            Id = request.Id,
            LoginFullName = _sessionManager.LoginFullName,
            LoginID = UserId,
            Message = request.Message.Trim(),
            ReportNo = request.ReportNo,
            Subject = request.Subject.Trim(),
            ToCompanyIdsArray = request.ToCompanyIdsArray,
            ToLoginsArray = request.ToLoginsArray,
            LoginEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty,
            ClientId = ClientId,
            Resources = resources,
            ReplyTos = _configuration.GetValue<string>(AppSettingKeys.SendToSupplier)
        }));
    }

    [HttpGet("{BOMNo}/items")]
    public async Task<IActionResult> GetBOMItem(int BOMNo)
    {
        return Ok(await _mediator.Send(new GetBomItemsQuery
        {
            IsPoHub = _sessionManager.GetBool(SessionKey.IsPOHub),
            BOMNo = BOMNo,
            ClientId = _sessionManager.GetInt32(SessionKey.ClientID),
            LoginId = _sessionManager.GetInt32(SessionKey.LoginID),
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty
        }));
    }
    [HttpPut("update-bom-status-to-closed")]
    public async Task<IActionResult> UpdateBOMStatusToClosed(UpdateBomStatusToClosedRequest request)
    {
        return Ok(await _mediator.Send(new UpdateBomStatusToClosedCommand
        {
            BomId = request.Id,
            BomStatus = (int)BOMStatus.Closed,
            UpdateBy = UserId
        }));
    }


    [HttpDelete("delete-temp-pvv-questions/{idGenerated}")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> DeleteTempPvvBomItems(string idGenerated, CancellationToken cancellationToken)
    {
        var query = new DeletePvvAnswerTempCommand
        {
            IdGenerated = idGenerated,
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }
    [HttpPost("export-to-csv")]
    public async Task<IActionResult> ExportToCSV(ExportToCsvRequest request)
    {
        /*
         * Due to this feature is not implemented, temporarily remove resources add to reduce duplicate.
         * Create ResourceService later to reuse
        */
        var resources = new List<(string key, string value)>();
        var data = await _mediator.Send(new CreateExportToCsvCommand
        {
            Id = request.Id,
            ClientID = ClientId,
            CurrencyCode = request.CurrencyCode,
            Export = "E",
            Report = Core.Enums.Report.RequirementWithBOM,
            Resources = resources,
            LoginFullName = _sessionManager.LoginFullName
        });
        return File(data.Data!.File, "text/csv", data.Data.FileName);
    }

    [HttpDelete("delete-bom-item")]
    public async Task<IActionResult> DeleteBomItemAsync(DeleteBomItemRequest request)
    {
        return Ok(await _mediator.Send(new DeleteBomItemCommand
        {
            BomId = request.BomId,
            RequirementId = request.RequirementId,
            LoginId = UserId,
            ClientId = ClientId
        }));
    }

    [HttpPost("apply-partwatch")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_CustomerRequirement_MainInformation_Edit)]
    public async Task<IActionResult> ApplyPartWatchAsync([FromBody] ApplyPartWatchRequest request)
    {
        var command = new ApplyPartWatchCommand
        {
            BOMId = request.BOMId,
            ReqIds = request.ReqIds,
            LoginId = UserId,
            ClientId = ClientId,
            UpdateByName = $"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}",
            SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail),
            Subject = _messageLocalizer["PartWatchMatchSubject"]
        };

        var result = await _mediator.Send(command);

        if (result.Success)
        {
            return Ok(result);
        }

        return BadRequest(result);
    }

    [HttpPost("expedite-notes")]
    public async Task<IActionResult> CreateHUBRFQExpediteNote([FromBody] CreateHubRfqExpediteNoteRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var command = new CreateExpediteNoteCommand()
        {
            HUBRFQId = request.HUBRFQId,
            EmailSendTo = request.EmailSendTo,
            ExpediteNotes = request.ExpediteNotes,
            UpdatedBy = UserId
        };

        var result = await _mediator.Send(command);

        if (result.Success)
        {
            return Ok(result);
        }
        return BadRequest(result);
    }

    [HttpPost("export-to-excel")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
    public async Task<IActionResult> ExportToExcelAsync([FromBody] ExportToExcelRequest request, CancellationToken cancellationToken)
    {
        var query = new ExportToExcelQuery
        {
            Id = request.Id,
            ClientId = ClientId
        };

        var result = await _mediator.Send(query, cancellationToken);

        var fileBytes = _exportService.ExportToExcelHUBRFQ(result.Data ?? []);

        Response.Headers.TryAdd("Content-Disposition", $"attachment; filename=\"HUBRFQ_SourcingResultTemplate.xlsx\"");

        return File(fileBytes, "text/csv");
    }

    [HttpPost("unrelease-bom-item")]
    public async Task<IActionResult> UnReleaseBomItemAsync(UnReleaseBomItemRequest request)
    {
        if (!_sessionManager.IsPOHub)
        {
            return BadRequest("Only POHub users are authorized to unrelease BOM items.");
        }

        var command = new UnReleaseBomItemCommand
        {
            BomId = request.BomId,
            RequirementId = request.RequirementId
        };
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpGet("item-details/{id}")]
    public async Task<IActionResult> GetBomItemDetails(int id)
    {
        var query = new GetBOMItemDetailFlowQuery(
            id,
            _sessionManager.ClientID ?? 0,
            _sessionManager.IsPOHub,
            _sessionManager.ClientCurrencyID ?? 0,
            _sessionManager.ClientCurrencyCode ?? "USD",
            CultureInfo.CurrentCulture
        );

        var response = await _mediator.Send(query);
        return Ok(response);
    }

    [HttpGet("lytica-manufacturer")]
    public async Task<IActionResult> GetLyticaManufacturer([FromQuery] string rsManufacturerName, [FromQuery] int customerRequirementId)
    {
        var query = new GetLyticaManufacturerQuery(customerRequirementId, rsManufacturerName);
        var response = await _mediator.Send(query);
        return Ok(response);
    }

    [HttpPost("delete-part-watch")]
    public async Task<IActionResult> DeletePartWatch(DeletePartWatchHUBRFQCommand request)
    {
        request.ClientID = ClientId;
        request.LoginID = UserId;
        var response = await _mediator.Send(request);
        return Ok(response);
    }

    [HttpGet("{bomId}/assignment-history")]
    public async Task<IActionResult> GetAssignmentHistory(int bomId)
    {
        var result = await _mediator.Send(new GetBomAssignmentHistoryQuery
        {
            BomId = bomId
        });
        return Ok(result);
    }

    [HttpPut("no-bid")]
    public async Task<IActionResult> UpdateCustomerRequirementNoBid([FromBody] UpdateCustomerRequirementNoBidRequest request)
    {
        var command = new UpdateCustomerRequirementNoBidFlowCommand
        {
            CustomerRequirementId = request.CustomerRequirementId,
            UpdatedBy = UserId,
            BomId = request.BomId,
            Notes = request.NoBidNotes,
            LoginEmail = LoginEmail,
            Sender = _sessionManager.LoginFullName,
            LoginId = UserId,
            BomCode = request.BomCode,
            BomName = request.BomName,
            BomCompanyName = request.BomCompanyName,
            BomCompanyNo = request.BomCompanyNo,
            SalesManName = request.SalesManName,
            SalesManNo = request.SalesManNo,
            Subject = _messageLocalizer["BOMNoBid"],
            StatusMessage = _miscLocalizer["HUBRFQIsReleased"],
            ClientId = ClientId,
            ClientName = _sessionManager.GetString(SessionKey.ClientName) ?? string.Empty
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpPut("recall-no-bid")]
    public async Task<IActionResult> RecallCustomerRequirementNoBid([FromBody] RecallCustomerRequirementNoBidRequest request)
    {
        var command = new RecallCustomerRequirementNoBidCommand
        {
            CustomerRequirementId = request.CustomerRequirementId,
            UpdatedBy = UserId
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpPut("update-bom")]
    public async Task<IActionResult> UpdateBom([FromBody] UpdateBomRequest request, CancellationToken cancellationToken)
    {
        var bomResponse = await _mediator.Send(new BOMDetailsQuery()
        {
            BOMId = request.BOMId,
        }, cancellationToken);

        var bom = bomResponse.Data!.FirstOrDefault();

        if (bom == null)
        {
            var errorResponse = new BaseResponse<int>
            {
                Success = false,
                Message = "BOM not found."
            };
            return BadRequest(errorResponse);
        }

        var command = new UpdateBomCommand
        {
            BOMId = request.BOMId,
            BOMName = $"{request.Name}-{sessionManager.ClientID}",
            Notes = request.Notes,
            Inactive = request.Inactive,
            UpdatedBy = sessionManager.LoginID,
            ContactNo = request.Contact,
            CurrencyNo = request.CurrencyNo,
            CurrentSupplier = request.CurrentSupplier,
            QuoteRequired = request.QuoteRequired,
            AS9120 = request.AS9120,
            Contact2Id = request.Contact2,
            ClientNo = sessionManager.ClientID ?? 0,
            BOMCode = bom.BOMCode,
            CompanyId = bom.CompanyId
        };

        return Ok(await _mediator.Send(command, cancellationToken));
    }

    [HttpPut("release-sourcing-result/{id}")]
    public async Task<IActionResult> ReleaseSourcingResult(int id)
    {
        if (!IsPOHub)
        {
            return BadRequest("Only POHub users are authorized to release sourcing result.");
        }
        var request = new ReleaseSourcingResultCommand
        {
            SourcingResultID = id,
            UpdatedBy = UserId
        };
        var response = await _mediator.Send(request);
        return Ok(response);
    }

    [HttpPut("update-customer-requirement-by-bom-id")]
    [ValidateAntiForgeryToken]
    // [ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_MainInformation_Edit)]
    public async Task<IActionResult> UpdateCustomerRequirementByBomId([FromBody] UpdateCustRequirementByBomIDRequest request)
    {
        var command = new UpdateCustRequirementByBomIDCommand
        {
            BomId = request.BomId,
            UpdatedBy = request.UpdatedBy ?? UserId,
            ClientNo = request.ClientNo ?? ClientId,
            ReqIds = request.ReqIds,
            BOMStatus = request.BOMStatus
        };

        var response = await _mediator.Send(command);
        return Ok(response);
    }
}
