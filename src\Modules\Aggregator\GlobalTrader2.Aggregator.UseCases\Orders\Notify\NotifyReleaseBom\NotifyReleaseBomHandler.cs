using GlobalTrader2.Aggregator.UseCases.Helper;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.GetListForBOMSourcingResult.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingLog;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Queries.GetSourcingResultQuotes;
using GlobalTrader2.Settings.UseCases.SecuritySettings.SecurityUsers.Queries;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseAll
{
    public class NotifyReleaseBomHandler(IMediator mediator, IEmailService emailService, IRazorViewToStringService razorViewToStringService, IMapper mapper) : IRequestHandler<NotifyReleaseBomCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IEmailService _emailService = emailService;
        private readonly IRazorViewToStringService _razorViewToStringService = razorViewToStringService;
        private readonly IMapper _mapper = mapper;

        public async Task<BaseResponse<int>> Handle(NotifyReleaseBomCommand request, CancellationToken cancellationToken)
        {
            var getUserQuery = new GetSecurityUserProfileQuery(request.SenderLoginNo);
            var getUserResponse = await _mediator.Send(getUserQuery, cancellationToken);

            // Process individual logins
            var recipients = request.ToLogins
                .Select(x => new RecipientRequest(
                    Value: x,
                    Type: (int)MailMessageAddressType.Individual
                )).ToList();

            // Process mail groups
            foreach (var groupId in request.ToGroups)
            {
                recipients.Add(new RecipientRequest(
                    Value: groupId,
                    Type: (int)MailMessageAddressType.Group
                ));
            }

            var contentInternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/_NotifyReleaseBom", new NotifyReleaseBom()
            {
                HyperLink = request.HUBRFQHyperlink,
                BomName = request.BOMName,
            });

            var sendMessageCommand = new SendNewMessageCommand(request.Subject, contentInternal, recipients, request.SenderLoginNo, request.SenderName)
            {
                CompanyId = request.BomCompanyNo,
                Message = contentInternal,
                Recipients = recipients,
                SenderLoginNo = request.SenderLoginNo,
                SenderName = request.SenderName,
                Subject = request.Subject
            };
            var result = await _mediator.Send(sendMessageCommand, cancellationToken);

            var bom = await _mediator.Send(new GetBOMDetailsQuery() { Id = request.BOMId, LoginId = request.SenderLoginNo }, cancellationToken);

            var reqs = await _mediator.Send(new GetBOMListForCustomerRequirementQuery() { BOMNo = request.BOMId, ClientID = request.ClientId, IsPoHub = false }, cancellationToken);

            IList<ListForBomSourcingResultDto> sourcingData;

            if (request.IsReleasedAll)
            {
                var response = await _mediator.Send(new GetListForBomSourcingResultQuery
                {
                    BOMNo = request.BOMId,
                    IsPoHub = request.IsPoHub
                }, cancellationToken);

                sourcingData = response.Data ?? [];
            }
            else
            {
                var response = await _mediator.Send(new GetListForBOMCustomerRequirementQuery
                {
                    ClientId = request.ClientId,
                    CustomerRequirementId = request.RequirementID ?? 0,
                    IsPOHub = request.IsPoHub,
                    ClientCurrencyCode = request.ClientCurrencyCode,
                    CultureInfo = request.CultureInfo,
                    LoginId = request.LoginId,
                    ClientCurrencyID = request.ClientCurrencyID
                }, cancellationToken);

                sourcingData = _mapper.Map<List<ListForBomSourcingResultDto>>(response.Data ?? []);
            }

            var enhancedSourcingResults = await EnhanceSourcingResultsAsync(sourcingData, cancellationToken);

            var templateModel = new ReleaseAllNotifyTemplate
            {
                HUBRFQStatus = request.HUBRFQStatus,
                Code = request.Code,
                Name = bom.Data.Name,
                Contact = bom.Data.ContactName,
                Company = bom.Data.Company,
                Currency = bom.Data.CurrencyCode,
                QuoteRequired = bom.Data.QuoteRequired ?? DateTime.Now,
                ClientCurrencyCode = request.ClientCurrencyCode,
                Requirements = [.. reqs.Data],
                SourcingResults = enhancedSourcingResults
            };

            var contentExternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/NotifyReleaseAllBom", templateModel);

            var loginPreferenceResponse = await _mediator.Send(new LoginPreferenceDetailsCommand { LoginNo = request.SenderLoginNo }, cancellationToken);
            var toEmail = getUserResponse.Data?.EMail;
            var sendEmail = loginPreferenceResponse.Data?.SendEmail;
            if (sendEmail is true && !string.IsNullOrWhiteSpace(toEmail))
            {
                await _emailService.TrySendEmailAsync(request.SenderEmail!, [toEmail], [], [], request.Subject, contentExternal, [], [], cancellationToken);
            }
            return result;
        }

        private async Task<List<EnhancedListForBomSourcingResultDto>> EnhanceSourcingResultsAsync(
            IList<ListForBomSourcingResultDto> sourcingResults,
            CancellationToken cancellationToken)
        {
            var enhancedResults = new List<EnhancedListForBomSourcingResultDto>();

            foreach (var sourcingResult in sourcingResults)
            {
                var enhanced = await EnhanceSingleSourcingResultAsync(sourcingResult, cancellationToken);
                enhancedResults.Add(enhanced);
            }

            return enhancedResults;
        }

        private async Task<EnhancedListForBomSourcingResultDto> EnhanceSingleSourcingResultAsync(
            ListForBomSourcingResultDto sourcingResult,
            CancellationToken cancellationToken)
        {
            // Use mapper to copy all base properties
            var enhanced = _mapper.Map<EnhancedListForBomSourcingResultDto>(sourcingResult);

            // Get quote numbers for this sourcing result
            enhanced.QuoteNumbers = await GetQuoteNumbersAsync(sourcingResult.SourcingResultId, cancellationToken);

            // Calculate highlighting based on ReReleased flag and change logs
            await SetHighlightingPropertiesAsync(enhanced, sourcingResult, cancellationToken);

            // Calculate converted price in base currency
            enhanced.ConvertedPriceInBaseCurrency = await CalculateConvertedPriceAsync(
                sourcingResult.Price,
                sourcingResult.CurrencyNo);

            return enhanced;
        }



        private async Task<List<string>> GetQuoteNumbersAsync(int sourcingResultId, CancellationToken cancellationToken)
        {
            var quotesQuery = new GetSourcingResultQuotesQuery { SourcingResultId = sourcingResultId };
            var quotesResponse = await _mediator.Send(quotesQuery, cancellationToken);

            if (quotesResponse.Success && quotesResponse.Data != null)
            {
                return quotesResponse.Data.Select(q => q.QuoteNumber.ToString()).Where(qn => !string.IsNullOrEmpty(qn)).ToList();
            }

            return new List<string>();
        }

        private async Task SetHighlightingPropertiesAsync(EnhancedListForBomSourcingResultDto enhanced, ListForBomSourcingResultDto source, CancellationToken cancellationToken)
        {
            // Initialize all highlighting to false
            enhanced.HighlightPartOrNotes = false;
            enhanced.HighlightManufacturer = false;
            enhanced.HighlightProductOrPackage = false;
            enhanced.HighlightQuantityOrDeliveryDate = false;
            enhanced.HighlightUpliftPrice = false;
            enhanced.HighlightRegion = false;
            enhanced.HighlightEstimatedShippingCost = false;

            // Only check for highlighting if item was re-released
            if (source.ReReleased == 1)
            {
                // Get sourcing log to check for change notes
                var sourcingLogQuery = new GetSourcingLogQuery { SourcingResultId = source.SourcingResultId };
                var sourcingLogResponse = await _mediator.Send(sourcingLogQuery, cancellationToken);

                if (sourcingLogResponse.Success && sourcingLogResponse.Data != null)
                {
                    var sourcingLog = sourcingLogResponse.Data.FirstOrDefault();
                    if (sourcingLog?.ChangeNotes != null)
                    {
                        // Parse change notes (comma-separated values) to determine highlighting
                        var changeNotes = sourcingLog.ChangeNotes.Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(note => note.Trim())
                            .ToList();

                        // Apply highlighting based on V1 logic
                        enhanced.HighlightPartOrNotes = changeNotes.Any(note =>
                            note.Equals("PartNo", StringComparison.OrdinalIgnoreCase) ||
                            note.Equals("Notes", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightManufacturer = changeNotes.Any(note =>
                            note.Equals("Manufacturer", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightProductOrPackage = changeNotes.Any(note =>
                            note.Equals("Product", StringComparison.OrdinalIgnoreCase) ||
                            note.Equals("Package", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightQuantityOrDeliveryDate = changeNotes.Any(note =>
                            note.Equals("Quantity", StringComparison.OrdinalIgnoreCase) ||
                            note.Equals("DeliveryDate", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightUpliftPrice = changeNotes.Any(note =>
                            note.Equals("UpliftPrice", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightRegion = changeNotes.Any(note =>
                            note.Equals("Region", StringComparison.OrdinalIgnoreCase));

                        enhanced.HighlightEstimatedShippingCost = changeNotes.Any(note =>
                            note.Equals("EstimatedShippingCost", StringComparison.OrdinalIgnoreCase));
                    }
                }
            }
        }

        private async Task<double?> CalculateConvertedPriceAsync(double price, int currencyNo)
        {
            var convertedValue = await _mediator.ConvertValueToBaseCurrencyAsync(price, currencyNo, DateTime.Now);
            return convertedValue;
        }
    }
}
