﻿<html>
<head>
    <title></title>
    <style type="text/css">

.Headerclass {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 13px;
	font-style:normal;
	font-weight:bold;
	color:#FFFFFF;
}
.Headerclass2 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 13px;
	font-style:normal;
	font-weight:bold;
	}
.Textclass
{
font-family:Arial, Helvetica, sans-serif;
font-size: 11px;
font-style:normal;
}
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11px;
	font-style: normal;
	font-weight: bold;
}
.style2 {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: 24px;
}

</style>
</head>
<body class="email">
    <table width="100%" border="1" cellspacing="2" cellpadding="2" bordercolor="#000000">
        <tr> <td colspan="2"><center><h3>#HUBRFQSTATUS#</h3> </center> </td></tr>
        <tr>
            <td class="Textclass" style=" width:50%; ">
                <b >Code </b> : #Code#
            </td>
            <td class="Textclass">
                <b>Contact</b> : #Contact#
            </td>
        </tr>
        <tr>
            <td class="Textclass">
                <b>Name</b> : #Name# &nbsp;
            </td>
            <td class="Textclass">
                <b>Currency</b> : #Currency#
            </td>
        </tr>
        <tr>
            <td class="Textclass">
                <b>Company</b> : #Company# &nbsp;
            </td>
            <td class="Textclass">
                <b>Quote Required </b> : #QuoteRequired#
            </td>
        </tr>
        <!--<tr>
            <td colspan="2">
                &nbsp;
            </td>

        </tr>-->
    </table>
    <br />
    <div> <b>Customer Requirement</b></div>
    <table width="100%" border="1" cellspacing="2" cellpadding="2" bordercolor="#000000">
        <tr style="background-color:#F5F5F5;">
            <th style="text-align: left; vertical-align: top" class="Textclass">Req <br /> Type</th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Quantity <br /> Traceability</th>
            <th style="text-align: left; vertical-align: top" class="Textclass">Part No <br /> Customer Part</th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Mfr <br /> DC </th>
            <th style="text-align: left; vertical-align: top" class="Textclass">Customer <br /> Required </th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Target Price <br /> Salesperson </th>
        </tr>
        #trline#
    </table>
    <br />
    <div> <b>Quotes to client</b></div>
    <table width="100%" border="1" cellspacing="2" cellpadding="2" bordercolor="#000000">
        <tr style="background-color:#F5F5F5;">
            <th style="text-align: left; vertical-align: top" class="Textclass">Supplier Type <br /> Related Quotes</th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> PartNo <br /> Notes</th>
            <th style="text-align: left; vertical-align: top" class="Textclass">MFR <br /> Date Code</th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Product <br /> Package </th>
            <th style="text-align: left; vertical-align: top" class="Textclass">Date Offered <br /> By </th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Quantity <br /> Delivery Date </th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Unit Sell Price <br /> Base Currency </th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Price Request <br /> Region </th>
            <th style="text-align: left; vertical-align: top" class="Textclass"> Estimated Shipping Cost <br /> Base Currency  </th>
        </tr>
        #trSrline#
    </table>
</body>
</html>