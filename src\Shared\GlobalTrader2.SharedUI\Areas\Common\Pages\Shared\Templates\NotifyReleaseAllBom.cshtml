﻿@using GlobalTrader2.Core.Enums
@using GlobalTrader2.Core.Helpers
@model GlobalTrader2.Dto.Templates.ReleaseAllNotifyTemplate
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title></title>
    <style type="text/css">
        body {
            font-family: Arial, Helvetica, sans-serif;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            border: 1px solid #000000;
            padding: 6px;
            vertical-align: top;
            font-size: 11px;
        }

        th {
            background-color: #F5F5F5;
            font-weight: bold;
        }

        h3 {
            font-size: 18px;
        }

        .header-class {
            font-size: 13px;
            font-weight: bold;
            color: #FFFFFF;
        }

        .header-class-2 {
            font-size: 13px;
            font-weight: bold;
        }

        .style-1 {
            font-size: 11px;
            font-weight: bold;
        }

        .style-2 {
            font-size: 24px;
            font-weight: bold;
        }

        .highlight {
            background-color: yellow;
        }
    </style>
</head>
<body class="email">
    <table>
        <tr style="display:none;">
            <th></th>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center;">
                <h3>@Model.HUBRFQStatus</h3>
            </td>
        </tr>
        <tr>
            <td><b>Code</b>: @Model.Code</td>
            <td><b>Contact</b>: @Model.Contact</td>
        </tr>
        <tr>
            <td><b>Name</b>: @Model.Name</td>
            <td><b>Currency</b>: @Model.Currency</td>
        </tr>
        <tr>
            <td><b>Company</b>: @Model.Company</td>
            <td><b>Quote Required</b>: @Model.QuoteRequired.ToString("d")</td>
        </tr>
    </table>

    <div><b>Customer Requirement</b></div>
    <table>
        <tr>
            <th>Req <br /> Type</th>
            <th>Quantity <br /> Traceability</th>
            <th>Part No <br /> Customer Part</th>
            <th>Mfr <br /> DC</th>
            <th>Customer <br /> Required</th>
            <th>Target Price <br /> Salesperson</th>
        </tr>
        @foreach (var req in Model.Requirements)
        {
            if (req.HasHubSourcingResult == true && req.POHubReleaseBy > 0)
            {
                <tr>
                    <td>@req.CustomerRequirementNumber <br />@req.ReqTypeText</td>
                    <td>@req.Quantity <br />@req.ReqForTraceabilityText</td>
                    <td>@Functions.GetPartWithAlternate(req.Part, req.AlternateStatus, req.Alternate) <br />@req.CustomerPart</td>
                    <td>@req.ManufacturerCode <br />@req.DateCode</td>
                    <td>@req.ClientName <br />@req.DatePromised.ToString("d")</td>
                    <td>@Functions.FormatCurrency(req.ConvertedTargetValue, req.CurrencyCode) <br />@req.SalesmanName</td>
                </tr>
            }
        }
    </table>

    <div><b>Quotes to client</b></div>
    <table>
        <tr>
            <th>Supplier Type <br /> Related Quotes</th>
            <th>PartNo <br /> Notes</th>
            <th>MFR <br /> Date Code</th>
            <th>Product <br /> Package</th>
            <th>Date Offered <br /> By</th>
            <th>Quantity <br /> Delivery Date</th>
            <th>Unit Sell Price <br /> Base Currency</th>
            <th>Price Request <br /> Region</th>
            <th>Estimated Shipping Cost <br /> Base Currency</th>
        </tr>
        @foreach (var sr in Model.SourcingResults)
        {
            <tr>
                <td>@sr.SupplierType <br /> @string.Join(", ", sr.QuoteNumbers)</td>
                <td class="@(sr.HighlightPartOrNotes ? "highlight" : "")">
                    @sr.Part <br /> @Html.Raw(Functions.ReplaceLineBreaks(sr.Notes))
                </td>
                <td class="@(sr.HighlightManufacturer ? "highlight" : "")">
                    @sr.ManufacturerName <br /> @sr.DateCode
                </td>
                <td class="@(sr.HighlightProductOrPackage ? "highlight" : "")">
                    @sr.ProductName <br /> @sr.PackageName
                </td>
                <td>
                    @sr.OfferStatusChangeDate?.ToString("d") <br /> @sr.OfferStatusChangeEmployeeName
                </td>
                <td class="@(sr.HighlightQuantityOrDeliveryDate ? "highlight" : "")">
                    @sr.Quantity <br /> @(sr.DeliveryDate?.ToString("d") ?? "")
                </td>
                <td class="@(sr.HighlightUpliftPrice ? "highlight" : "")">
                    @sr.Price.ToString("F5") @sr.CurrencyCode <br />
                    @(sr.ConvertedPriceInBaseCurrency?.ToString("F5") ?? "") @Model.ClientCurrencyCode
                </td>
                <td class="@(sr.HighlightRegion ? "highlight" : "")">
                    @(sr.IsPQ ? "YES" : "-") <br /> @sr.RegionName
                </td>
                <td class="@(sr.HighlightEstimatedShippingCost ? "highlight" : "")">
                    @(sr.EstimatedShippingCost?.ToString("F5") ?? "") @sr.CurrencyCode
                </td>
            </tr>
        }
    </table>
</body>
</html>
