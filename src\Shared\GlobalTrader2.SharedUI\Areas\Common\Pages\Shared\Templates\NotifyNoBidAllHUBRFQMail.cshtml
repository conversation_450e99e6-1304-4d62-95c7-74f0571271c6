@model GlobalTrader2.Dto.Templates.NoBidAllNotifyTemplate
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <style type="text/css">
        .header-class {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 13px;
            font-weight: bold;
            color: #FFFFFF;
        }

        .header-class-2 {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 13px;
            font-weight: bold;
        }

        .text-class {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
        }

        .style-1 {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            font-weight: bold;
        }

        .style-2 {
            font-family: Arial, Helvetica, sans-serif;
            font-weight: bold;
            font-size: 24px;
        }
    </style>
</head>
<body class="email">
    <table width="100%" border="1" cellspacing="2" cellpadding="2" bordercolor="#000000">
        <tr>
            <td colspan="2">
                <center>
                    <h3>@Model.HUBRFQStatus</h3>
                </center>
            </td>
        </tr>
        <tr>
            <td class="text-class" style="width: 50%;">
                <b>Code</b>: @Model.Code
            </td>
            <td class="text-class">
                <b>Contact</b>: @Model.Contact
            </td>
        </tr>
        <tr>
            <td class="text-class">
                <b>Name</b>: @Model.Name
            </td>
            <td class="text-class">
                <b>Currency</b>: @Model.Currency
            </td>
        </tr>
        <tr>
            <td class="text-class">
                <b>Company</b>: @Model.Company
            </td>
            <td class="text-class">
                <b>Quote Required</b>: @Model.QuoteRequired.ToString("d")
            </td>
        </tr>
    </table>
    <br />
    <div>
        <b>Customer Requirement</b>
    </div>
    <table width="100%" border="1" cellspacing="2" cellpadding="2" bordercolor="#000000">
        <tr style="background-color: #F5F5F5;">
            <th class="text-class">Req <br /> Type </th>
            <th class="text-class">Quantity <br /> Traceability </th>
            <th class="text-class">Part No <br /> Customer Part </th>
            <th class="text-class">Mfr <br /> DC </th>
            <th class="text-class">Customer <br /> Required </th>
            <th class="text-class">Target Price <br /> Salesperson </th>
        </tr> @foreach (var req in Model.Requirements) {
        @if (req.IsNoBid is true)
        {
            <tr>
                <td class="text-class">@req.CustomerRequirementNumber <br />@req.ReqTypeText </td>
                <td class="text-class">@req.Quantity <br />@req.ReqForTraceabilityText </td>
                <td class="text-class">@req.Part <br />@req.CustomerPart </td>
                <td class="text-class">@req.ManufacturerCode <br />@req.DateCode </td>
                <td class="text-class">@req.ClientName <br />@req.DatePromised.ToString("d") </td>
                <td class="text-class">@req.ConvertedTargetValue?.ToString("F5") <br />@req.SalesmanName </td>
            </tr>
        }
                }
    </table>
    <br />
    <!-- Not found binded data in V1 for quote to client -->
    <div>
        <b>Quotes to client</b>
    </div>
    <table width="100%" border="1" cellspacing="2" cellpadding="2" bordercolor="#000000">
        <tr style="background-color: #F5F5F5;">
            <th class="text-class">Supplier Type <br /> Related Quotes </th>
            <th class="text-class">PartNo <br /> Notes </th>
            <th class="text-class">MFR <br /> Date Code </th>
            <th class="text-class">Product <br /> Package </th>
            <th class="text-class">Date Offered <br /> By </th>
            <th class="text-class">Quantity <br /> Delivery Date </th>
            <th class="text-class">Unit Sell Price <br /> Base Currency </th>
            <th class="text-class">Price Request <br /> Region </th>
            <th class="text-class">Estimated Shipping Cost <br /> Base Currency </th>
        </tr>
    </table>
</body>
</html>